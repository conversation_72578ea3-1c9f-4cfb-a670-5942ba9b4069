package installment

import (
	"context"
	"errors"
	"testing"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/stretchr/testify/suite"
	"go.uber.org/mock/gomock"

	"gitlab.zalopay.vn/fin/installment/installment-service/config"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/management/model"
	adapter_mocks "gitlab.zalopay.vn/fin/installment/installment-service/internal/management/usecase/mocks/adapter"
	repository_mocks "gitlab.zalopay.vn/fin/installment/installment-service/internal/management/usecase/mocks/repository"
	transaction_mocks "gitlab.zalopay.vn/fin/installment/installment-service/internal/management/usecase/mocks/transaction"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/keygen/mock"
)

type InstallmentTestSuite struct {
	suite.Suite
	ctrl              *gomock.Controller
	usecase           *Usecase
	mockInstRepo      *repository_mocks.MockInstallmentRepo
	mockCIMBSvc       *adapter_mocks.MockCIMBService
	mockTxn           *transaction_mocks.MockTransaction
	mockDistLock      *adapter_mocks.MockDistributedLock
	mockJobTaskMgmt   *adapter_mocks.MockJobTaskMgmt
	mockAccountSvc    *adapter_mocks.MockAccountService
	mockPaymentEvtPub *adapter_mocks.MockPaymentEventPublisher
	mockRedisKeyGen   *mock.MockRedisKeyGenerator
}

func TestInstallmentSuite(t *testing.T) {
	// Run the suite
	suite.Run(t, new(InstallmentTestSuite))

	// Add an extra sleep at the end of all tests to ensure goroutines complete
	// This helps prevent the test suite from failing due to goroutines still running
	// time.Sleep(1 * time.Second)
}

func (s *InstallmentTestSuite) SetupTest() {
	s.ctrl = gomock.NewController(s.T())
	s.mockInstRepo = repository_mocks.NewMockInstallmentRepo(s.ctrl)
	s.mockCIMBSvc = adapter_mocks.NewMockCIMBService(s.ctrl)
	s.mockTxn = transaction_mocks.NewMockTransaction(s.ctrl)
	s.mockDistLock = adapter_mocks.NewMockDistributedLock(s.ctrl)
	s.mockJobTaskMgmt = adapter_mocks.NewMockJobTaskMgmt(s.ctrl)
	s.mockAccountSvc = adapter_mocks.NewMockAccountService(s.ctrl)
	s.mockPaymentEvtPub = adapter_mocks.NewMockPaymentEventPublisher(s.ctrl)
	s.mockRedisKeyGen = mock.NewMockRedisKeyGenerator(s.ctrl)

	// Create a test configuration with necessary values
	mockConfig := &config.Management{
		EarlyDischarge: &config.Management_EarlyDischarge{
			// Add default values or customize as needed for tests
		},
	}

	s.usecase = NewUsecase(
		s.mockTxn,
		s.mockDistLock,
		s.mockInstRepo,
		s.mockJobTaskMgmt,
		s.mockCIMBSvc,
		s.mockAccountSvc,
		s.mockPaymentEvtPub,
		s.mockRedisKeyGen,
		mockConfig,
		log.DefaultLogger,
	)
}

func (s *InstallmentTestSuite) TearDownTest() {
	// Give goroutines time to complete before finishing the test
	// This helps prevent race conditions in tests that use goroutines
	time.Sleep(1 * time.Second)
	s.ctrl.Finish()
}

func (s *InstallmentTestSuite) TestSyncEarlyDischarge_Success() {
	ctx := context.Background()

	// Setup test data
	inst := createTestInstallmentInfo()
	earlyDischarge := createTestPartnerEarlyDischarge(false)
	updatedInst := createTestInstallmentInfo()
	updatedInst.EarlyDischarge = buildEarlyDischargeInfo(earlyDischarge)

	// Mock expectations
	s.mockCIMBSvc.EXPECT().QueryEarlyDischargeByID(ctx, inst.ZalopayID, inst.PartnerInstID).Return(earlyDischarge, nil)
	s.mockTxn.EXPECT().BeginTx(ctx).Return(ctx, nil)
	s.mockTxn.EXPECT().RollbackTx(ctx).Return(nil)
	s.mockInstRepo.EXPECT().GetInstallmentByIDForUpdate(ctx, inst.ID).Return(inst, nil)
	s.mockInstRepo.EXPECT().UpdateEarlyDischargeInfo(ctx, gomock.Any()).Return(nil)
	s.mockTxn.EXPECT().CommitTx(ctx).Return(nil)

	// Execute
	result, err := s.usecase.SyncEarlyDischarge(ctx, inst)

	// Assert
	s.NoError(err)
	s.NotNil(result)
	s.NotNil(result.EarlyDischarge)
	s.NotNil(result.EarlyDischarge.Details)
	s.Equal(earlyDischarge.DischargeData.TotalDischargeAmount, result.EarlyDischarge.Details.TotalDischargeAmount)
}

func (s *InstallmentTestSuite) TestSyncEarlyDischarge_SuccessWithDischargeComplete() {
	ctx := context.Background()

	// Setup test data
	inst := createTestInstallmentInfo()
	earlyDischarge := createTestPartnerEarlyDischarge(true) // HasDischarged = true
	updatedInst := createTestInstallmentInfo()
	updatedInst.EarlyDischarge = buildEarlyDischargeInfo(earlyDischarge)
	updatedInst.DischargeStatus = model.DischargeStatusComplete

	// Mock expectations
	s.mockCIMBSvc.EXPECT().QueryEarlyDischargeByID(ctx, inst.ZalopayID, inst.PartnerInstID).Return(earlyDischarge, nil)
	s.mockTxn.EXPECT().BeginTx(ctx).Return(ctx, nil)
	s.mockTxn.EXPECT().RollbackTx(ctx).Return(nil)
	s.mockInstRepo.EXPECT().GetInstallmentByIDForUpdate(ctx, inst.ID).Return(inst, nil)
	s.mockInstRepo.EXPECT().UpdateEarlyDischargeInfo(ctx, gomock.Any()).Return(nil)
	s.mockTxn.EXPECT().CommitTx(ctx).Return(nil)

	// Execute
	result, err := s.usecase.SyncEarlyDischarge(ctx, inst)

	// Assert
	s.NoError(err)
	s.NotNil(result)
	s.Equal(model.DischargeStatusComplete, result.DischargeStatus)
	s.NotNil(result.EarlyDischarge)
}

func (s *InstallmentTestSuite) TestSyncEarlyDischarge_CIMBServiceError() {
	ctx := context.Background()

	// Setup test data
	inst := createTestInstallmentInfo()
	expectedError := errors.New("CIMB service error")

	// Mock expectations
	s.mockCIMBSvc.EXPECT().QueryEarlyDischargeByID(ctx, inst.ZalopayID, inst.PartnerInstID).Return(nil, expectedError)

	// Execute
	result, err := s.usecase.SyncEarlyDischarge(ctx, inst)

	// Assert
	s.Error(err)
	s.Nil(result)
	s.Equal(expectedError, err)
}
